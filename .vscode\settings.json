{"editor.formatOnSave": true, "editor.defaultFormatter": "trunk.io", "trunk.enableWindows": true, "files.insertFinalNewline": false, "files.trimFinalNewlines": false, "cmake.configureOnOpen": false, "[cpp]": {"editor.defaultFormatter": "trunk.io"}, "[powershell]": {"editor.defaultFormatter": "ms-vscode.powershell"}, "files.associations": {"*.cpp": "cpp", "cmath": "cpp", "segger_rtt_conf.h": "c", "segger_rtt_printf.c": "cpp", "typeinfo": "cpp", "unordered_map": "cpp", "fstream": "cpp", "istream": "cpp", "numeric": "cpp", "ostream": "cpp", "sstream": "cpp", "chrono": "cpp", "array": "cpp", "atomic": "cpp", "bit": "cpp", "cctype": "cpp", "clocale": "cpp", "codecvt": "cpp", "condition_variable": "cpp", "csignal": "cpp", "cstdarg": "cpp", "cstddef": "cpp", "cstdint": "cpp", "cstdio": "cpp", "cstdlib": "cpp", "cstring": "cpp", "ctime": "cpp", "cwchar": "cpp", "cwctype": "cpp", "deque": "cpp", "list": "cpp", "map": "cpp", "unordered_set": "cpp", "vector": "cpp", "exception": "cpp", "algorithm": "cpp", "functional": "cpp", "iterator": "cpp", "memory": "cpp", "memory_resource": "cpp", "optional": "cpp", "random": "cpp", "ratio": "cpp", "string": "cpp", "string_view": "cpp", "system_error": "cpp", "tuple": "cpp", "type_traits": "cpp", "utility": "cpp", "initializer_list": "cpp", "iomanip": "cpp", "iosfwd": "cpp", "iostream": "cpp", "limits": "cpp", "mutex": "cpp", "new": "cpp", "stdexcept": "cpp", "streambuf": "cpp", "thread": "cpp", "cinttypes": "cpp"}}