#include "detect_ele.h"
#include "configuration.h"

#define debug_detect_ele

typedef enum {
    STATE_STARTUP,
    STATE_RUNNING,          // 正常运行
    STATE_OVERCURRENT,      // 过电流
    STATE_STARTUP_OVERCURRENT  // 从零开始的过电流
} LineState;

typedef struct {
    float rate_current;    // 瞬时过电流阈值
    float overcurrent_multiple;     // 延时时间ms
    float overcurrent_delaytime;    // 瞬时过电流阈值
    float overcurrent_delaytime_fromzero;     // 延时时间ms
} Current_setting;

Current_setting current_setting;
LineState lineState = STATE_STARTUP;
uint32_t task_interval = 1;
uint32_t startup_lasttime = 0;
uint32_t overcurrent_lasttime = 0;// 计算过载阈值时间的起始值

detect_ele::detect_ele(const char *name) : concurrency::OSThread(name) {

    //初始化
    bool initstatus = init();

    #ifdef debug_detect_ele
    LOG_DEBUG("detect_ele init %d",initstatus);
    #endif

    setInterval(10 *1000); //10S后启动这个任务。
}

bool detect_ele::init(){
    bool init = false;
    //传感器初始化
    //init =

    return init;
}

int32_t detect_ele::runOnce(){
    static std::vector<CurrentRecord> records;  // 使用vector存储电流记录
    const size_t MAX_RECORDS = 1000;           // 最大记录数

    // 获取当前时间
    uint32_t current_time = millis();

    // 从传感器获取RMS和瞬时电流值的接口

    float rms_value = 0;    // 需要实现getRMSCurrent()函数
    float inst_value = 0;  // 需要实现getInstCurrent()函数

    // float rms_value = getRMSCurrent();    // 需要实现getRMSCurrent()函数
    // float inst_value = getInstCurrent();  // 需要实现getInstCurrent()函数

    if(!rms_value){
        #ifdef debug_detect_ele
        LOG_DEBUG("rms_value is 0");
        #endif
        lineState = STATE_STARTUP;  // 设置为启动状态
        startup_lasttime = current_time;
        if (!records.empty())
        {
            records.clear();  // 清空所有记录
        }
        return task_interval;
    }
    
    // 添加新记录到vector
    CurrentRecord new_record = {
        .rms_current = rms_value,
        .inst_current = inst_value,
        .timestamp = current_time
    };

    // 维护固定大小的记录容器
    if (records.size() >= MAX_RECORDS) {
        records.erase(records.begin());  // 删除最旧的记录
    }
    records.push_back(new_record);      // 添加新记录

    // 根据电流设置判断过电流状态
    
    uint32_t overcurrent_duration = 0;
    if (overcurrent_lasttime)
    {
        overcurrent_duration = current_time - overcurrent_lasttime; // 这个值为0说明值是无效的
    }
    // 计算从启动开始的持续时间
    uint32_t startup_duration = 0;
    if (startup_lasttime)
    {
        startup_duration = current_time - startup_lasttime; // 这个值为0说明值是无效的
    }

    // 改变状态机
    // 启动状态判断
    if (startup_duration <= current_setting.overcurrent_delaytime_fromzero && startup_duration ) {
        // 在启动时间窗口内，检测过电流但不改变状态
        if (rms_value > current_setting.rate_current * current_setting.overcurrent_multiple) {
            lineState = STATE_STARTUP;
            LOG_DEBUG("Overcurrent detected in startup window! RMS: %f, Duration: %lu ms", rms_value, startup_duration);
        }
        else{
            lineState = STATE_RUNNING;
            LOG_DEBUG("Normal running");
        }
    } else {
            //启动过电流的判断
            if (lineState == STATE_STARTUP_OVERCURRENT && 
                rms_value > current_setting.rate_current * current_setting.overcurrent_multiple)
            {
                lineState = STATE_STARTUP_OVERCURRENT;
                LOG_DEBUG("Overcurrent after startup window! RMS: %f", rms_value);
                //STATE_STARTUP_OVERCURRENT具体操作
                
                return task_interval;
            }
            //启动状态到启动过电流判断
            else if(rms_value > current_setting.rate_current * current_setting.overcurrent_multiple && 
                    lineState == STATE_STARTUP ){
                    //第一次判断超出启动窗口时间
                    lineState = STATE_STARTUP_OVERCURRENT;
                    LOG_DEBUG("First overcurrent after startup window! RMS: %f", rms_value);
                    //STATE_STARTUP_OVERCURRENT具体操作

                    return task_interval;
            }
            // 正常运行判断和过流判断
            if (rms_value > current_setting.rate_current * current_setting.overcurrent_multiple )
            {
                if (overcurrent_duration)
                {
                    if (overcurrent_duration > current_setting.overcurrent_delaytime)
                    {
                        lineState = STATE_OVERCURRENT;
                        LOG_DEBUG("Transitioned to overcurrent state! RMS: %f, Duration: %lu ms", rms_value, overcurrent_duration);

                    }
                    else{
                        lineState = STATE_RUNNING;
                        LOG_DEBUG("Exceeds the rated current multiple but does not reach the time threshold");
                    }
                }
                else {
                    overcurrent_lasttime = current_time;  // 记录第一次过电流的时间
                    LOG_DEBUG("First overcurrent detected, starting timer");
                }
                

            }
            else if (rms_value <= current_setting.rate_current * current_setting.overcurrent_multiple) {
                //电流恢复正常
                lineState = STATE_RUNNING;
                LOG_DEBUG("Current returned to normal: %f", rms_value);
            }
    }
    
    #ifdef debug_detect_ele
    LOG_DEBUG("Current Record - RMS: %.2f, Inst: %.2f, Time: %lu", 
              new_record.rms_current, 
              new_record.inst_current, 
              new_record.timestamp);
    #endif

    // 状态处理
    switch (lineState) {
        case STATE_STARTUP:
            // 启动过电流状态处理
            break;

        case STATE_RUNNING:
            break;

        case STATE_OVERCURRENT:
            // 过电流状态处理
            break;

    }

    return task_interval;
}

