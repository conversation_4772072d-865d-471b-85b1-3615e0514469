#pragma once


#include "mesh/NodeDB.h"



class detect_ele :public concurrency::OSThread
{
  public: 
  explicit detect_ele(const char *name);

  bool init();
  virtual int32_t runOnce() override;




  // Ele_error setting;
  // int detect_times = 0;
  // int setting_times = 0;
  // float current = 0;
  // float rmscurrent = 0;
  // float last_rmscurrent = 0;
  // std::vector<Ele_error > Ele_queue;
};
