#pragma once


#include "mesh/NodeDB.h"

typedef enum {
  STATE_STARTUP,
  STATE_RUNNING,          // 正常运行
  STATE_OVERCURRENT,      // 过电流
  STATE_OVERCURRENT_DURATION  // 过电流恢复
} LineState;

typedef struct {
  float rate_current;    // 瞬时过电流阈值
  float overcurrent_multiple;     // 过电流倍率
  float overcurrent_delaytime;    // 从0延时时间ms
  float overcurrent_delaytime_fromzero;     // 延时时间ms
  float alarm_interval;    // 报警时间间隔ms
  float report_interval;   // 上报时间间隔ms
} Current_setting;

typedef struct {
  float rms_current;   // 电流有效值
  float inst_current;  // 电流瞬时值
  uint32_t timestamp;  // 时间戳(微秒)
} CurrentRecord;

class detect_ele :public concurrency::OSThread
{
  public: 
  explicit detect_ele(const char *name);

  bool init();
  virtual int32_t runOnce() override;


  std::vector<CurrentRecord> records;  // 使用vector存储电流记录
  uint32_t alarm_lastSentToMesh = 0;
  uint32_t report_lastSentToMesh = 0;
  // Ele_error setting;
  // int detect_times = 0;
  // int setting_times = 0;
  // float current = 0;
  // float rmscurrent = 0;
  // float last_rmscurrent = 0;
  // std::vector<Ele_error > Ele_queue;
};
extern detect_ele *Detect_ele;