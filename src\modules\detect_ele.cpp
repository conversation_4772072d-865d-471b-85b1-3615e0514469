#include "detect_ele.h"
#include "Telemetry/PowerTelemetry.h"
#include "configuration.h"

#define debug_detect_ele
detect_ele *Detect_ele;


Current_setting current_setting;
LineState lineState = STATE_STARTUP;
uint32_t task_interval = 1;
uint32_t startup_lasttime = 0;
uint32_t overcurrent_lasttime = 0;// 计算过载阈值时间的起始值


detect_ele::detect_ele(const char *name) : concurrency::OSThread(name) {

    //初始化
    bool initstatus = init();

    #ifdef debug_detect_ele
    LOG_DEBUG("detect_ele init %d",initstatus);
    #endif

    current_setting = {
        .rate_current = 600.0f,              // 额定电流1000A
        .overcurrent_multiple = 1.0f,         // 过电流倍率1.5倍
        .overcurrent_delaytime = 20.0f,     // 过电流延时时间5秒
        .overcurrent_delaytime_fromzero = 20.0f,  // 从零启动延时时间2秒
        .alarm_interval = 1 *1000,
        .report_interval = 10 *1000
    };

    setInterval(10 *1000); //10S后启动这个任务。
}

bool detect_ele::init(){
    bool init = false;
    //传感器初始化
    //init =

    return init;
}

int32_t detect_ele::runOnce(){
    const size_t MAX_RECORDS = 1000;           // 最大记录数

    // 获取当前时间
    uint32_t current_time = millis();

    // 从传感器获取RMS和瞬时电流值的接口

    float rms_value = 0;    // 需要实现getRMSCurrent()函数
    float inst_value = 0;  // 需要实现getInstCurrent()函数

    // float rms_value = getRMSCurrent();    // 需要实现getRMSCurrent()函数
    // float inst_value = getInstCurrent();  // 需要实现getInstCurrent()函数
    // 模拟启动电流数据
    static float current_data[100] = {
        0, 50, 800, 800, 800, 800, 800, 800, 800, 800,     // 启动阶段
        800, 800, 800, 800, 800, 800, 800, 800, 800, 800,  // 稳定运行
        610, 620, 650, 680, 700, 680, 660, 640, 620, 610,  // 超载阶段
        580, 570, 560, 550, 540, 530, 520, 510, 500, 490,  // 回归正常
        1600, 1700, 1800, 1900, 2000, 1950, 1800, 1700, 1600, 1500,  // 瞬间短路
        800, 600, 500, 480, 460, 450, 440, 430, 420, 410,  // 断电回落
        400, 390, 380, 370, 360, 350, 340, 330, 320, 310,  // 稳定运行
        1800, 1900, 2000, 2100, 2200, 2150, 2100, 2050, 2000, 1950,  // 突然短路
        600, 580, 560, 540, 520, 500, 480, 460, 440, 420,  // 回归正常
        600, 580, 550, 500, 480, 460, 450, 440, 430, 420   // 最后正常
    };
    static int data_index = 0;
    rms_value = current_data[data_index];
    inst_value = rms_value * 1.414f; // 估算峰值
    data_index = (data_index + 1) % 100; // 循环使用数据
    
    if(!rms_value){
        #ifdef debug_detect_ele
        LOG_DEBUG("rms_value is 0");
        #endif
        lineState = STATE_STARTUP;  // 设置为启动状态
        startup_lasttime = current_time;
        if (!records.empty())
        {
            records.clear();  // 清空所有记录
        }
        return task_interval;
    }

    // 添加新记录到vector
    CurrentRecord new_record = {
        .rms_current = rms_value,
        .inst_current = inst_value,
        .timestamp = current_time
    };

    // 维护固定大小的记录容器
    if (records.size() >= MAX_RECORDS) {
        records.erase(records.begin());  // 删除最旧的记录
    }
    records.push_back(new_record);      // 添加新记录

    // 根据电流设置判断过电流状态
    
    uint32_t overcurrent_duration = 0;
    if (overcurrent_lasttime)
    {
        overcurrent_duration = current_time - overcurrent_lasttime; // 这个值为0说明值是无效的
    }
    // 计算从启动开始的持续时间
    uint32_t startup_duration = 0;
    if (startup_lasttime)
    {
        startup_duration = current_time - startup_lasttime; // 这个值为0说明值是无效的
    }
    // 改变状态机
    // 启动状态判断
    if (startup_duration <= current_setting.overcurrent_delaytime_fromzero && startup_duration ) {
        // 在启动时间窗口内，检测过电流但不改变状态
        if (rms_value > current_setting.rate_current * current_setting.overcurrent_multiple) {
            lineState = STATE_OVERCURRENT_DURATION;
            if (!overcurrent_lasttime) {
                // 第一次检测到过流，记录开始时间
                overcurrent_lasttime = current_time;
                LOG_DEBUG("STATE_OVERCURRENT_DURATION-- overcurrent timer start");
            } else {
                // 已经在过流状态，记录持续时间
                LOG_DEBUG("STATE_OVERCURRENT_DURATION-- Continuing overcurrent duration: %lu ms", overcurrent_duration);
            }
        }
        else{
            lineState = STATE_STARTUP;
            overcurrent_lasttime = 0;
            LOG_DEBUG("STARTUP-- normal running");
        }
    } else {
            if (lineState == STATE_STARTUP)
            {
                lineState = STATE_RUNNING;
                LOG_DEBUG("RUNNING-- Startup end");
            }
            if (lineState == STATE_RUNNING)
            {
                // 判断当前状态
                if (rms_value > current_setting.rate_current * current_setting.overcurrent_multiple )
                {
                    if (!overcurrent_duration)
                    {
                        lineState = STATE_OVERCURRENT_DURATION;
                        overcurrent_lasttime = current_time;  // 记录第一次过电流的时间
                        LOG_DEBUG("OVERCURRENT_DURATION-- First overcurrent detected");
                    }
                }
                else if (rms_value == 0){
                    lineState = STATE_STARTUP;
                    startup_lasttime = current_time;
                    LOG_DEBUG("STARTUP-- current is 0");
                }
                else if(rms_value > 0 && rms_value <= current_setting.rate_current * current_setting.overcurrent_multiple){
                    //电流恢复正常
                    lineState = STATE_RUNNING;
                    overcurrent_lasttime = 0;
                    LOG_DEBUG("RUNNING-- normal running");
                }
            }
            if (lineState == STATE_OVERCURRENT_DURATION)
            {
                if (rms_value > current_setting.rate_current * current_setting.overcurrent_multiple)
                {
                    if (overcurrent_duration > current_setting.overcurrent_delaytime)
                    {
                        lineState = STATE_OVERCURRENT;
                        LOG_DEBUG("OVERCURRENT-- Duration: %lu ms",overcurrent_duration);
                    }
                    else{
                        lineState = STATE_OVERCURRENT_DURATION;
                        LOG_DEBUG("OVERCURRENT_DURATION-- Duration: %lu ms",overcurrent_duration);
                    }
                }
                else if (rms_value == 0){
                    lineState = STATE_STARTUP;
                    startup_lasttime = current_time;
                    LOG_DEBUG("STARTUP-- current is 0");
                }
                else if(rms_value > 0 && rms_value <= current_setting.rate_current * current_setting.overcurrent_multiple){
                    //电流恢复正常
                    lineState = STATE_RUNNING;
                    overcurrent_lasttime = 0;
                    LOG_DEBUG("RUNNING-- normal running");
                }
            
            }
            if (lineState == STATE_OVERCURRENT_DURATION)

            //启动过电流的判断
            if (lineState == STATE_STARTUP_OVERCURRENT && 
                rms_value > current_setting.rate_current * current_setting.overcurrent_multiple)
            {
                LOG_DEBUG("Startup-- conitinous overcurrent RMS: %f duration: %lu ms", rms_value, overcurrent_duration);
                //STATE_STARTUP_OVERCURRENT具体操作
                
                return task_interval;
            }
    }
    
    #ifdef debug_detect_ele
    LOG_DEBUG("Current Record - RMS: %.2f, Inst: %.2f, Time: %lu", 
              new_record.rms_current, 
              new_record.inst_current, 
              new_record.timestamp);
    #endif

    // 状态处理
    switch(lineState) {
        case STATE_STARTUP:
            break;
        case STATE_RUNNING:
            if ((report_lastSentToMesh == 0) || 
                (airTime->isTxAllowedAirUtil() && millis() - report_lastSentToMesh > current_setting.report_interval))
            {
                report_lastSentToMesh = millis();
                powertelemetry->Report(meshtastic_FaultType_FAULT_NONE,new_record);
            }
            else{
                uint32_t remaining_time = current_setting.report_interval - (millis() - report_lastSentToMesh);
                LOG_DEBUG("Report airTime not allowed, remaining time: %lu ms", remaining_time);
            }
            break;
        case STATE_OVERCURRENT_DURATION:

            break;
        case STATE_OVERCURRENT:
            // 过电流状态处理
            if ((alarm_lastSentToMesh == 0) || 
                (airTime->isTxAllowedAirUtil() && millis() - alarm_lastSentToMesh > current_setting.alarm_interval))
            {
                alarm_lastSentToMesh = millis();
                powertelemetry->Overcurrent(meshtastic_FaultType_FAULT_PERMANENT,new_record);
            }
            else{
                uint32_t remaining_time = current_setting.alarm_interval - (millis() - alarm_lastSentToMesh);
                LOG_DEBUG("Alarm overcurrent airTime not allowed, remaining time: %lu ms", remaining_time);
            }
            break;
    }

    return task_interval;
}

