#pragma once

#include "configuration.h"

#if !MESHTASTIC_EXCLUDE_POWER_TELEMETRY

#include "../mesh/generated/meshtastic/telemetry.pb.h"
#include "NodeDB.h"
#include "ProtobufModule.h"
#include <OLEDDisplay.h>
#include <OLEDDisplayUi.h>
#include "../detect_ele.h"
#define debug_powertelemetry

class PowerTelemetryModule : private concurrency::OSThread, public ProtobufModule<meshtastic_Telemetry>
{
    CallbackObserver<PowerTelemetryModule, const meshtastic::Status *> nodeStatusObserver =
        CallbackObserver<PowerTelemetryModule, const meshtastic::Status *>(this, &PowerTelemetryModule::handleStatusUpdate);

  public:
    PowerTelemetryModule()
        : concurrency::OSThread("PowerTelemetry"),
          ProtobufModule("PowerTelemetry", meshtastic_PortNum_TELEMETRY_APP, &meshtastic_Telemetry_msg)
    {
        lastMeasurementPacket = nullptr;
        nodeStatusObserver.observe(&nodeStatus->onNewStatus);
        LOG_INFO("Power Telemetry: init");
        setIntervalFromNow(10 * 1000);
    }
    bool Overcurrent(meshtastic_FaultType faulttype,CurrentRecord record);
    bool Report(meshtastic_FaultType faulttype,CurrentRecord record);

    bool sendTelemetry(NodeNum dest = NODENUM_BROADCAST, bool wantReplies = false);
  protected:
    /** Called to handle a particular incoming message
    @return true if you've guaranteed you've handled this message and no other handlers should be considered for it
    */
    virtual bool handleReceivedProtobuf(const meshtastic_MeshPacket &mp, meshtastic_Telemetry *p) override;
    virtual int32_t runOnce() override;
    /** Called to get current Power telemetry data
    @return true if it contains valid data
    */
    bool getPowerTelemetry(meshtastic_Telemetry *m);
    virtual meshtastic_MeshPacket *allocReply() override;
    /**
     * Send our Telemetry into the mesh
     */
  private:
    bool firstTime = 1;
    meshtastic_MeshPacket *lastMeasurementPacket;
    uint32_t sendToPhoneIntervalMs = SECONDS_IN_MINUTE * 1000; // Send to phone every minute
    uint32_t lastSentToMesh = 0;
    uint32_t lastSentToPhone = 0;
    uint32_t sensor_read_error_count = 0;
};

extern PowerTelemetryModule *powertelemetry;
#endif