#include "configuration.h"

#if !MESHTASTIC_EXCLUDE_POWER_TELEMETRY

#include "../mesh/generated/meshtastic/telemetry.pb.h"
#include "Default.h"
#include "MeshService.h"
#include "NodeDB.h"
#include "PowerFSM.h"
#include "PowerTelemetry.h"
#include "RTC.h"
#include "Router.h"
#include "main.h"
#include "power.h"
#include "sleep.h"
#include "target_specific.h"

#define FAILED_STATE_SENSOR_READ_MULTIPLIER 10
#define DISPLAY_RECEIVEID_MEASUREMENTS_ON_SCREEN true

#include "graphics/ScreenFonts.h"
#include <Throttle.h>

PowerTelemetryModule *powertelemetry;

meshtastic_FaultType faulttype;

int32_t PowerTelemetryModule::runOnce()
{
    if (sleepOnNextExecution == true) {
        sleepOnNextExecution = false;
        uint32_t nightyNightMs = Default::getConfiguredOrDefaultMs(moduleConfig.telemetry.power_update_interval,
                                                                   default_telemetry_broadcast_interval_secs);
        LOG_DEBUG("Sleep for %ims, then awake to send metrics again", nightyNightMs);
        doDeepSleep(nightyNightMs, true, false);
    }

    /*
        Uncomment the preferences below if you want to use the module
        without having to configure it from the PythonAPI or WebUI.
    */

    moduleConfig.telemetry.power_measurement_enabled = 1;
    // moduleConfig.telemetry.power_screen_enabled = 1;
    // moduleConfig.telemetry.power_update_interval = 10;

    if (!(moduleConfig.telemetry.power_measurement_enabled)) {
        // If this module is not enabled, and the user doesn't want the display screen don't waste any OSThread time on it
        return disable();
    }

    uint32_t sendToMeshIntervalMs = Default::getConfiguredOrDefaultMsScaled(
        moduleConfig.telemetry.power_update_interval, default_telemetry_broadcast_interval_secs, numOnlineNodes);

    if (firstTime) {
        // This is the first time the OSThread library has called this function, so do some setup
        firstTime = 0;
        uint32_t result = UINT32_MAX;

#if HAS_TELEMETRY && !defined(ARCH_PORTDUINO)
        if (moduleConfig.telemetry.power_measurement_enabled) {
            LOG_INFO("Power Telemetry: init");
            result = 1000;
        }

        // it's possible to have this module enabled, only for displaying values on the screen.
        // therefore, we should only enable the sensor loop if measurement is also enabled
        return result == UINT32_MAX ? disable() : setStartDelay();
#else
        return disable();
#endif
    } else {
        // if we somehow got to a second run of this module with measurement disabled, then just wait forever
        // if (!moduleConfig.telemetry.power_measurement_enabled)
        //     return disable();

        // if (((lastSentToMesh == 0) || !Throttle::isWithinTimespanMs(lastSentToMesh, sendToMeshIntervalMs)) &&
        //     airTime->isTxAllowedAirUtil()) {
        //     sendTelemetry();
        //     lastSentToMesh = millis();
        // } else if (((lastSentToPhone == 0) || !Throttle::isWithinTimespanMs(lastSentToPhone, sendToPhoneIntervalMs)) &&
        //            (service->isToPhoneQueueEmpty())) {
        //     // Just send to phone when it's not our time to send to mesh yet
        //     // Only send while queue is empty (phone assumed connected)
        //     sendTelemetry(NODENUM_BROADCAST, true);
        //     lastSentToPhone = millis();
        // }
    }
    return min(sendToPhoneIntervalMs, sendToMeshIntervalMs);
}



bool PowerTelemetryModule::handleReceivedProtobuf(const meshtastic_MeshPacket &mp, meshtastic_Telemetry *t)
{
    if (t->which_variant == meshtastic_Telemetry_power_metrics_tag) {
#ifdef DEBUG_PORT
        const char *sender = getSenderShortName(mp);

        LOG_INFO("(Received from %s): ch1_voltage=%.1f, ch1_current=%.1f , faulttype = %d",
                 sender, t->variant.power_metrics.ch1_voltage, t->variant.power_metrics.ch1_current, t->variant.power_metrics.fault);
#endif
        // release previous packet before occupying a new spot
        if (lastMeasurementPacket != nullptr)
            packetPool.release(lastMeasurementPacket);

        lastMeasurementPacket = packetPool.allocCopy(mp);
    }

    return false; // Let others look at this message also if they want
}



bool PowerTelemetryModule::getPowerTelemetry(meshtastic_Telemetry *m)
{
    bool valid = false;
    m->time = getTime();
    m->which_variant = meshtastic_Telemetry_power_metrics_tag;
    m->variant.power_metrics = meshtastic_PowerMetrics_init_zero;
    m->variant.power_metrics.has_ch1_current = true;
    m->variant.power_metrics.ch1_current = 111;

    m->variant.power_metrics.fault = faulttype;
    valid = true;

    return valid;
}

meshtastic_MeshPacket *PowerTelemetryModule::allocReply()
{
    if (currentRequest) {
        auto req = *currentRequest;
        const auto &p = req.decoded;
        meshtastic_Telemetry scratch;
        meshtastic_Telemetry *decoded = NULL;
        memset(&scratch, 0, sizeof(scratch));
        if (pb_decode_from_bytes(p.payload.bytes, p.payload.size, &meshtastic_Telemetry_msg, &scratch)) {
            decoded = &scratch;
        } else {
            LOG_ERROR("Error decoding PowerTelemetry module!");
            return NULL;
        }
        // Check for a request for power metrics
        if (decoded->which_variant == meshtastic_Telemetry_power_metrics_tag) {
            meshtastic_Telemetry m = meshtastic_Telemetry_init_zero;
            if (getPowerTelemetry(&m)) {
                LOG_INFO("Power telemetry reply to request");
                return allocDataProtobuf(m);
            } else {
                return NULL;
            }
        }
    }

    return NULL;
}

bool PowerTelemetryModule::sendTelemetry(NodeNum dest, bool phoneOnly)
{
    meshtastic_Telemetry m = meshtastic_Telemetry_init_zero;
    m.which_variant = meshtastic_Telemetry_power_metrics_tag;
    m.time = getTime();
    if (getPowerTelemetry(&m)) {
        LOG_INFO("Send: ch1_voltage=%f, ch1_current=%f , faulttype = %d " ,
                 m.variant.power_metrics.ch1_voltage, m.variant.power_metrics.ch1_current, m.variant.power_metrics.fault);

        sensor_read_error_count = 0;

        meshtastic_MeshPacket *p = allocDataProtobuf(m);
        p->to = dest;
        p->decoded.want_response = false;
        if (config.device.role == meshtastic_Config_DeviceConfig_Role_SENSOR)
            p->priority = meshtastic_MeshPacket_Priority_RELIABLE;
        else
            p->priority = meshtastic_MeshPacket_Priority_BACKGROUND;
        // release previous packet before occupying a new spot
        if (lastMeasurementPacket != nullptr)
            packetPool.release(lastMeasurementPacket);

        lastMeasurementPacket = packetPool.allocCopy(*p);
        if (phoneOnly) {
            LOG_INFO("Send packet to phone");
            service->sendToPhone(p);
        } else {
            LOG_INFO("Send packet to mesh");
            service->sendToMesh(p, RX_SRC_LOCAL, true);

            if (config.device.role == meshtastic_Config_DeviceConfig_Role_SENSOR && config.power.is_power_saving) {
                LOG_DEBUG("Start next execution in 5s then sleep");
                sleepOnNextExecution = true;
                setIntervalFromNow(5000);
            }
        }
        return true;
    }
    return false;
}

bool PowerTelemetryModule::Overcurrent(meshtastic_FaultType faulttype,CurrentRecord record)
{
    //生成一个报警包
    meshtastic_Telemetry m = meshtastic_Telemetry_init_zero;
    m.which_variant = meshtastic_Telemetry_power_metrics_tag;
    m.variant.power_metrics = meshtastic_PowerMetrics_init_zero;
    m.time = record.timestamp;
    m.variant.power_metrics.has_ch1_current = true;
    m.variant.power_metrics.ch1_current = record.rms_current;
    m.variant.power_metrics.fault = faulttype;
    //打印内容
    LOG_INFO("Send: ch1_voltage=%f, ch1_current=%f , faulttype = %d " ,
                m.variant.power_metrics.ch1_voltage, m.variant.power_metrics.ch1_current, m.variant.power_metrics.fault);

    sensor_read_error_count = 0;

    meshtastic_MeshPacket *p = allocDataProtobuf(m);
    p->to = NODENUM_BROADCAST;
    p->decoded.want_response = false;
    //节省宽带。以后够用的话就开启，保证网络的可靠性
    p->want_ack = true;
    p->priority = meshtastic_MeshPacket_Priority_HIGH;
    // 保留最后一个发送的包。
    if (lastMeasurementPacket != nullptr)
        packetPool.release(lastMeasurementPacket);
    lastMeasurementPacket = packetPool.allocCopy(*p);
    //发送
    LOG_INFO("Send packet to mesh");
    service->sendToMesh(p, RX_SRC_LOCAL, true);
    return true;
}


bool PowerTelemetryModule::Report(meshtastic_FaultType faulttype,CurrentRecord record)
{
    //生成一个报警包
    meshtastic_Telemetry m = meshtastic_Telemetry_init_zero;
    m.which_variant = meshtastic_Telemetry_power_metrics_tag;
    m.variant.power_metrics = meshtastic_PowerMetrics_init_zero;
    m.time = record.timestamp;
    m.variant.power_metrics.has_ch1_current = true;
    m.variant.power_metrics.ch1_current = record.rms_current;
    m.variant.power_metrics.fault = faulttype;
    //打印内容
    LOG_INFO("Send: ch1_voltage=%f, ch1_current=%f , faulttype = %d " ,
                m.variant.power_metrics.ch1_voltage, m.variant.power_metrics.ch1_current, m.variant.power_metrics.fault);

    sensor_read_error_count = 0;

    meshtastic_MeshPacket *p = allocDataProtobuf(m);
    p->to = NODENUM_BROADCAST;
    p->decoded.want_response = false;
    //节省宽带。以后够用的话就开启，保证网络的可靠性
    p->want_ack = false;
    p->priority = meshtastic_MeshPacket_Priority_RELIABLE;
    // 保留最后一个发送的包。
    if (lastMeasurementPacket != nullptr)
        packetPool.release(lastMeasurementPacket);
    lastMeasurementPacket = packetPool.allocCopy(*p);
    //发送
    LOG_INFO("Send packet to mesh");
    service->sendToMesh(p, RX_SRC_LOCAL, true);
    LOG_INFO("Send packet to phone");
    service->sendToPhone(p);
    return true;
}


#endif